FROM apache/airflow:2.10.5-python3.9

USER airflow

COPY requirements.txt /requirements.txt

RUN pip install --no-cache-dir pip==23.3.1
RUN pip install --no-cache-dir tenacity==8.2.3
RUN pip install --no-cache-dir -r /requirements.txt

#FROM apache/airflow:2.10.5-python3.9

#USER airflow

#COPY requirements.txt /requirements.txt

#ARG AIRFLOW_VERSION=2.10.5
#ARG PYTHON_VERSION=3.9

#RUN pip install --no-cache-dir apache-airflow==${AIRFLOW_VERSION} \
#  --constraint "https://raw.githubusercontent.com/apache/airflow/constraints-${AIRFLOW_VERSION}/constraints-${PYTHON_VERSION}.txt" \
#  && pip install --no-cache-dir -r /requirements.txt \
#  --constraint "https://raw.githubusercontent.com/apache/airflow/constraints-${AIRFLOW_VERSION}/constraints-${PYTHON_VERSION}.txt"



#FROM apache/airflow:2.10.5
#ADD requirements.txt .
#RUN pip install apache-airflow==${AIRFLOW_VERSION} -r requirements.txt
#USER airflow
#COPY requirements.txt /requirements.txt
#RUN pip install --no-cache-dir -r /requirements.txt

#FROM apache/airflow:2.10.5

#USER airflow
#COPY requirements.txt /requirements.txt

#ARG AIRFLOW_VERSION=2.10.5
#ARG PYTHON_VERSION=3.12

#RUN pip install --no-cache-dir apache-airflow==${AIRFLOW_VERSION} \
#  --constraint "https://raw.githubusercontent.com/apache/airflow/constraints-${AIRFLOW_VERSION}/constraints-${PYTHON_VERSION}.txt" \
#  && pip install --no-cache-dir -r /requirements.txt \
#  --constraint "https://raw.githubusercontent.com/apache/airflow/constraints-${AIRFLOW_VERSION}/constraints-${PYTHON_VERSION}.txt"
