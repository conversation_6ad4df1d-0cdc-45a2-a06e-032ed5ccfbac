import json
from datetime import datetime
import requests
import pandas as pd
from io import BytesIO
import openpyxl

from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.hooks.base import BaseHook
from airflow.exceptions import AirflowException

# --- 1. Configuration ---
SHAREPOINT_HOSTNAME = "rakamin001.sharepoint.com"
SITE_RELATIVE_PATH = "/sites/Strategy"
DOCUMENTS_PATH = "/Shared Documents"

def get_access_token(**kwargs):
    """
    Authenticates with Microsoft Graph API to get an access token.
    Pushes the token to XComs for other tasks to use.
    """
    # Retrieve the connection details from Airflow
    conn = BaseHook.get_connection('sharepoint_graph_api')
    creds = json.loads(conn.extra)
    
    tenant_id = creds['tenant_id']
    client_id = creds['client_id']
    client_secret = creds['client_secret']

    token_url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token"
    
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    payload = {
        'client_id': client_id,
        'scope': 'https://graph.microsoft.com/.default',
        'client_secret': client_secret,
        'grant_type': 'client_credentials'
    }

    response = requests.post(token_url, headers=headers, data=payload)
    
    if response.status_code != 200:
        raise AirflowException(f"Failed to get access token: {response.text}")
        
    token_data = response.json()
    access_token = token_data.get('access_token')
    
    if not access_token:
        raise AirflowException("Access token not found in response.")
        
    print("Successfully acquired access token.")
    # Push the token to XComs
    kwargs['ti'].xcom_push(key='access_token', value=access_token)

def get_latest_folder(**kwargs):
    """
    Gets the latest updated folder from SharePoint.
    """
    ti = kwargs['ti']
    access_token = ti.xcom_pull(key='access_token', task_ids='get_access_token')

    if not access_token:
        raise AirflowException("Access token not found in XComs.")

    # Get folders in Shared Documents
    graph_api_endpoint = (
        f"https://graph.microsoft.com/v1.0/sites/{SHAREPOINT_HOSTNAME}:{SITE_RELATIVE_PATH}:"
        f"/drive/root:{DOCUMENTS_PATH}:/children"
    )

    headers = {'Authorization': f'Bearer {access_token}'}
    
    response = requests.get(graph_api_endpoint, headers=headers)
    
    if response.status_code != 200:
        raise AirflowException(f"Failed to get folders: {response.status_code} - {response.text}")

    data = response.json()
    folders = [item for item in data.get('value', []) if item.get('folder')]
    
    if not folders:
        raise AirflowException("No folders found in Shared Documents")
    
    # Sort by lastModifiedDateTime to get latest
    latest_folder = max(folders, key=lambda x: x['lastModifiedDateTime'])
    folder_name = latest_folder['name']
    
    print(f"Latest folder: {folder_name}")
    ti.xcom_push(key='latest_folder', value=folder_name)

def get_latest_xlsx_file(**kwargs):
    """
    Gets the latest xlsx file from the latest folder.
    """
    ti = kwargs['ti']
    access_token = ti.xcom_pull(key='access_token', task_ids='get_access_token')
    folder_name = ti.xcom_pull(key='latest_folder', task_ids='get_latest_folder')

    # Get files in the latest folder
    graph_api_endpoint = (
        f"https://graph.microsoft.com/v1.0/sites/{SHAREPOINT_HOSTNAME}:{SITE_RELATIVE_PATH}:"
        f"/drive/root:{DOCUMENTS_PATH}/{folder_name}:/children"
    )

    headers = {'Authorization': f'Bearer {access_token}'}
    response = requests.get(graph_api_endpoint, headers=headers)
    
    if response.status_code != 200:
        raise AirflowException(f"Failed to get files: {response.status_code} - {response.text}")

    data = response.json()
    xlsx_files = [item for item in data.get('value', []) 
                  if item.get('file') and item['name'].endswith('.xlsx')]
    
    if not xlsx_files:
        raise AirflowException(f"No xlsx files found in folder {folder_name}")
    
    # Get latest xlsx file
    latest_file = max(xlsx_files, key=lambda x: x['lastModifiedDateTime'])
    file_path = f"{DOCUMENTS_PATH}/{folder_name}/{latest_file['name']}"
    
    print(f"Latest xlsx file: {latest_file['name']}")
    ti.xcom_push(key='latest_file_path', value=file_path)

def extract_sharepoint_xlsx(**kwargs):
    """
    Downloads and extracts all sheets from the xlsx file.
    """
    ti = kwargs['ti']
    access_token = ti.xcom_pull(key='access_token', task_ids='get_access_token')
    file_path = ti.xcom_pull(key='latest_file_path', task_ids='get_latest_xlsx_file')

    # Download file content
    graph_api_endpoint = (
        f"https://graph.microsoft.com/v1.0/sites/{SHAREPOINT_HOSTNAME}:{SITE_RELATIVE_PATH}:"
        f"/drive/root:{file_path}:/content"
    )

    headers = {'Authorization': f'Bearer {access_token}'}
    response = requests.get(graph_api_endpoint, headers=headers)

    if response.status_code != 200:
        raise AirflowException(f"Failed to download file: {response.status_code} - {response.text}")

    # Read xlsx file with all sheets
    xlsx_content = BytesIO(response.content)
    excel_file = pd.ExcelFile(xlsx_content)
    
    all_sheets_data = {}
    for sheet_name in excel_file.sheet_names:
        df = pd.read_excel(xlsx_content, sheet_name=sheet_name)
        all_sheets_data[sheet_name] = df.to_dict('records')
        print(f"Sheet '{sheet_name}': {len(df)} rows, {len(df.columns)} columns")
    
    ti.xcom_push(key='all_sheets_data', value=all_sheets_data)
    print(f"Extracted {len(all_sheets_data)} sheets from {file_path}")

def process_xlsx_sheets(**kwargs):
    """
    Process all extracted sheets.
    """
    ti = kwargs['ti']
    all_sheets_data = ti.xcom_pull(key='all_sheets_data', task_ids='extract_sharepoint_xlsx')

    if not all_sheets_data:
        raise AirflowException("No sheets data found in XComs.")

    for sheet_name, data in all_sheets_data.items():
        df = pd.DataFrame(data)
        print(f"\nProcessing sheet: {sheet_name}")
        print(f"Shape: {df.shape}")
        print(df.head())
        
        # Add your processing logic here for each sheet
        # Example: save to different tables based on sheet name


with DAG(
    dag_id='sharepoint_latest_xlsx_pipeline',
    start_date=datetime(2023, 1, 1),
    schedule_interval='@daily',
    catchup=False,
    tags=['sharepoint', 'xlsx', 'latest'],
) as dag:
    
    get_token_task = PythonOperator(
        task_id='get_access_token',
        python_callable=get_access_token,
    )

    get_folder_task = PythonOperator(
        task_id='get_latest_folder',
        python_callable=get_latest_folder,
    )

    get_file_task = PythonOperator(
        task_id='get_latest_xlsx_file',
        python_callable=get_latest_xlsx_file,
    )

    extract_xlsx_task = PythonOperator(
        task_id='extract_sharepoint_xlsx',
        python_callable=extract_sharepoint_xlsx,
    )

    process_sheets_task = PythonOperator(
        task_id='process_xlsx_sheets',
        python_callable=process_xlsx_sheets,
    )

    # Define the task dependencies
    get_token_task >> get_folder_task >> get_file_task >> extract_xlsx_task >> process_sheets_task
