# Rakamin Airflow DAGs

Apache Airflow project with DAGs for data processing, ETL pipelines, and AI-powered document processing.

## 🚀 Quick Start

### 1. Clone Repository

```bash
git clone <repository-url>
cd rakamin-dag
```

### 2. Setup Environment

```bash
# Copy environment file
cp .env.example .env

# Edit with your configuration
nano .env
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Run with Docker

```bash
# Start Airflow
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f airflow-webserver
```

### 5. Access Airflow UI

Open browser: `http://localhost:8080`

- **Username:** `admin`
- **Password:** `admin`

## 🚀 Run DAGs

```bash
# Manual trigger
airflow dags trigger <dag_id>

# Or use Airflow UI
```

## 🐛 Troubleshooting

```bash
# Restart services
docker-compose down
docker-compose up -d

# Check logs
docker-compose logs airflow-webserver
```
