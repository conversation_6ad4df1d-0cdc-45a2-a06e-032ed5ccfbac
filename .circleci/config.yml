version: 2.1

orbs:
  gcp-cli: circleci/gcp-cli@3.3.1

jobs:
  deploy:
    docker:
      - image: cimg/base:stable
    steps:
      - checkout
      - gcp-cli/setup
      - run:
          name: Generate SSH key for gcloud (no passphrase)
          command: |
            if [ ! -f ~/.ssh/google_compute_engine ]; then
              ssh-keygen -t rsa -f ~/.ssh/google_compute_engine -N "" -C "circleci"
            fi
      - run:
          name: SSH to Google Compute Engine to docker compose down
          command: |
            gcloud compute ssh $INSTANCE_NAME --zone=$ZONE --command="cd $GCP_DEPLOY_PATH && sudo docker compose down"
      - run:
          name: Upload code to Google Compute Engine
          command: |
            rm -rf .git .github .gitignore
            mkdir deploy_dir
            shopt -s extglob
            cp -r !(.git|.github|.gitignore|deploy_dir) deploy_dir/
            gcloud compute scp --recurse --zone=$ZONE deploy_dir $INSTANCE_NAME:~/airflow
            gcloud compute ssh $INSTANCE_NAME --zone=$ZONE --command "sudo rsync -a ~/airflow/deploy_dir/ $GCP_DEPLOY_PATH/ && rm -rf ~/airflow/deploy_dir"
      - run:
          name: SSH to Google Compute Engine and deploy
          command: |
            gcloud compute ssh $INSTANCE_NAME --zone=$ZONE --command "cd $GCP_DEPLOY_PATH && sudo docker compose up -d --build"
      - run:
          name: Notify deployment success
          command: |
            echo "Deployment to $INSTANCE_NAME completed successfully!"
  deploy_to_lightsail:
    docker:
      - image: cimg/base:stable
    steps:
      - checkout
      - add_ssh_keys:
          fingerprints:
            - "SHA256:Myx9L6ACkgBOUi1Tz/gRpw7sp7jZGfNnCcenQCZEtfE"
      - run:
          name: Install rsync and openssh-client
          command: |
            sudo apt-get update && sudo apt-get install -y rsync openssh-client
      # STEP 1: Stop the old environment and give ownership back to the deploy user
      - run:
          name: Prepare remote host for deployment
          command: |
            ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null "$LIGHTSAIL_USER@$LIGHTSAIL_HOST" "
              set -ex # Exit on error, print commands
              cd $REMOTE_AIRFLOW_PATH
              
              echo 'Stopping existing services...'
              sudo docker compose down || true
              
              echo 'Reclaiming ownership for deployment user...'
              sudo chown -R ubuntu:ubuntu ./dags ./logs ./config ./plugins
            "
      - run:
          name: Rsync project to AWS Lightsail and handle DAGs conditionally
          command: |
            ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null "$LIGHTSAIL_USER@$LIGHTSAIL_HOST" "mkdir -p $REMOTE_AIRFLOW_PATH"
            rsync -avz --exclude='.git' --exclude='.github' --exclude='.gitignore' --exclude='venv' -e "ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" ./ "$LIGHTSAIL_USER@$LIGHTSAIL_HOST:$REMOTE_AIRFLOW_PATH/"
            # Now handle the DAG_FILE logic
            if [ -n "$DAG_FILE" ] && [ -f "dags/$DAG_FILE" ]; then
              echo "DAG_FILE is set and exists. Cleaning up other DAGs on remote and re-uploading only $DAG_FILE."
              ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null "$LIGHTSAIL_USER@$LIGHTSAIL_HOST" "find $REMOTE_AIRFLOW_PATH/dags -type f ! -name '$DAG_FILE' -delete"
              rsync -avz -e "ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" "dags/$DAG_FILE" "$LIGHTSAIL_USER@$LIGHTSAIL_HOST:$REMOTE_AIRFLOW_PATH/dags"
            fi
      - run:
          name: Set permissions for Airflow user
          command: |
            ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null "$LIGHTSAIL_USER@$LIGHTSAIL_HOST" "
              set -x
              cd $REMOTE_AIRFLOW_PATH
              sudo chown -R 50000:0 ./dags ./logs ./config ./plugins
            "
      - run:
          name: Set permissions on remote host and deploy
          command: |
            ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null "$LIGHTSAIL_USER@$LIGHTSAIL_HOST" "
              set -x
              cd $REMOTE_AIRFLOW_PATH
              sudo docker compose up -d --build
            "
      - run:
          name: Notify deployment success
          command: |
            echo "DAG $DAG_FILE deployed to $LIGHTSAIL_HOST successfully!"

workflows:
  version: 2
  deploy_workflow:
    jobs:
      - approval:
          type: approval
          filters:
            branches:
              only:
                - staging
      - deploy:
          name: Deploy to Google Compute Engine
          requires: [approval]
          filters:
            branches:
              only:
                - staging
  deploy_to_prod_workflow:
    jobs:
      - approval:
          type: approval
          filters:
            branches:
              only:
                - main
      - deploy_to_lightsail:
          name: Deploy DAG to AWS Lightsail
          requires: [approval]
          filters:
            branches:
              only:
                - main